{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "naming_series:", "creation": "2023-11-06 17:56:25.210449", "doctype": "DocType", "engine": "InnoDB", "field_order": ["organization_tab", "naming_series", "organization", "next_step", "column_break_ijan", "status", "deal_owner", "lost_reason", "lost_notes", "reject_reason", "reject_notes", "section_break_jgpm", "probability", "expected_deal_value", "deal_value", "column_break_kpxa", "expected_closure_date", "closed_date", "contacts_tab", "contacts", "contact", "lead_details_tab", "lead", "source", "column_break_wsde", "lead_name", "organization_details_section", "organization_name", "website", "no_of_employees", "job_title", "column_break_xbyf", "territory", "currency", "exchange_rate", "annual_revenue", "industry", "person_section", "salutation", "first_name", "last_name", "column_break_xjmy", "email", "mobile_no", "phone", "gender", "products_tab", "products", "section_break_ccbj", "total", "column_break_udbq", "net_total", "sla_tab", "sla", "sla_creation", "column_break_pfvq", "sla_status", "communication_status", "response_details_section", "response_by", "column_break_hpvj", "first_response_time", "first_responded_on", "log_tab", "status_change_log"], "fields": [{"fieldname": "organization", "fieldtype": "Link", "label": "Organization", "options": "CRM Organization"}, {"fieldname": "probability", "fieldtype": "Percent", "label": "Probability"}, {"fetch_from": ".annual_revenue", "fieldname": "annual_revenue", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Annual Revenue", "options": "currency"}, {"fetch_from": ".website", "fieldname": "website", "fieldtype": "Data", "label": "Website"}, {"fieldname": "next_step", "fieldtype": "Data", "label": "Next Step"}, {"fieldname": "lead", "fieldtype": "Link", "label": "Lead", "options": "CRM Lead"}, {"fieldname": "deal_owner", "fieldtype": "Link", "label": "Deal Owner", "options": "User"}, {"default": "CRM-DEAL-.YYYY.-", "fieldname": "naming_series", "fieldtype": "Select", "label": "Naming Series", "options": "CRM-DEAL-.YYYY.-"}, {"fieldname": "contacts_tab", "fieldtype": "Tab Break", "label": "Contacts"}, {"fieldname": "email", "fieldtype": "Data", "label": "Primary Email", "options": "Email", "read_only": 1}, {"fieldname": "mobile_no", "fieldtype": "Data", "label": "Primary Mobile No", "options": "Phone", "read_only": 1}, {"default": "Qualification", "fieldname": "status", "fieldtype": "Link", "in_list_view": 1, "label": "Status", "options": "CRM Deal Status", "reqd": 1, "search_index": 1}, {"fieldname": "contacts", "fieldtype": "Table", "label": "Contacts", "options": "CRM Contacts"}, {"fieldname": "organization_tab", "fieldtype": "Tab Break", "label": "Organization"}, {"fieldname": "sla_tab", "fieldtype": "Tab Break", "label": "SLA", "read_only": 1}, {"fieldname": "sla", "fieldtype": "Link", "label": "SLA", "options": "CRM Service Level Agreement"}, {"fieldname": "response_by", "fieldtype": "Datetime", "label": "Response By", "read_only": 1}, {"fieldname": "column_break_pfvq", "fieldtype": "Column Break"}, {"fieldname": "sla_status", "fieldtype": "Select", "label": "SLA Status", "options": "\nFirst Response Due\nFailed\nFulfilled", "read_only": 1}, {"fieldname": "sla_creation", "fieldtype": "Datetime", "label": "SLA Creation", "read_only": 1}, {"fieldname": "response_details_section", "fieldtype": "Section Break", "label": "Response Details"}, {"fieldname": "column_break_hpvj", "fieldtype": "Column Break"}, {"fieldname": "first_response_time", "fieldtype": "Duration", "label": "First Response Time", "read_only": 1}, {"fieldname": "first_responded_on", "fieldtype": "Datetime", "label": "First Responded On", "read_only": 1}, {"default": "Open", "fieldname": "communication_status", "fieldtype": "Link", "label": "Communication Status", "options": "CRM Communication Status"}, {"fetch_from": ".territory", "fieldname": "territory", "fieldtype": "Link", "label": "Territory", "options": "CRM Territory"}, {"fieldname": "source", "fieldtype": "Link", "label": "Source", "options": "CRM Lead Source"}, {"fieldname": "no_of_employees", "fieldtype": "Select", "label": "No. of Employees", "options": "1-10\n11-50\n51-200\n201-500\n501-1000\n1000+"}, {"fieldname": "job_title", "fieldtype": "Data", "label": "Job Title"}, {"fieldname": "phone", "fieldtype": "Data", "label": "Primary Phone", "options": "Phone", "read_only": 1}, {"fieldname": "log_tab", "fieldtype": "Tab Break", "label": "Log", "read_only": 1}, {"fieldname": "status_change_log", "fieldtype": "Table", "label": "Status Change Log", "options": "CRM Status Change Log"}, {"fieldname": "lead_name", "fieldtype": "Data", "label": "Lead Name"}, {"fieldname": "column_break_ijan", "fieldtype": "Column Break"}, {"fieldname": "lead_details_tab", "fieldtype": "Tab Break", "label": "Lead Details"}, {"fieldname": "column_break_wsde", "fieldtype": "Column Break"}, {"fieldname": "organization_details_section", "fieldtype": "Section Break", "label": "Organization Details"}, {"fieldname": "organization_name", "fieldtype": "Data", "label": "Organization Name"}, {"fieldname": "column_break_xbyf", "fieldtype": "Column Break"}, {"fieldname": "industry", "fieldtype": "Link", "label": "Industry", "options": "CRM Industry"}, {"fieldname": "person_section", "fieldtype": "Section Break", "label": "Person"}, {"fieldname": "salutation", "fieldtype": "Link", "label": "Salutation", "options": "Salutation"}, {"fieldname": "first_name", "fieldtype": "Data", "label": "First Name"}, {"fieldname": "last_name", "fieldtype": "Data", "label": "Last Name"}, {"fieldname": "column_break_xjmy", "fieldtype": "Column Break"}, {"fieldname": "gender", "fieldtype": "Link", "label": "Gender", "options": "Gender"}, {"fieldname": "contact", "fieldtype": "Link", "label": "Contact", "options": "Contact"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "products_tab", "fieldtype": "Tab Break", "label": "Products"}, {"fieldname": "products", "fieldtype": "Table", "label": "Products", "options": "CRM Products"}, {"fieldname": "section_break_ccbj", "fieldtype": "Section Break"}, {"fieldname": "column_break_udbq", "fieldtype": "Column Break"}, {"fieldname": "total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total", "options": "currency", "read_only": 1}, {"description": "Total after discount", "fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total", "options": "currency", "read_only": 1}, {"fieldname": "section_break_jgpm", "fieldtype": "Section Break"}, {"fieldname": "deal_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Deal Value", "options": "currency"}, {"fieldname": "column_break_kpxa", "fieldtype": "Column Break"}, {"fieldname": "lost_reason", "fieldtype": "Link", "label": "Lost Reason", "mandatory_depends_on": "eval: doc.status == \"Lost\"", "options": "CRM Lost Reason"}, {"fieldname": "lost_notes", "fieldtype": "Text", "label": "Lost Notes", "mandatory_depends_on": "eval: doc.lost_reason == \"Other\""}, {"fieldname": "reject_reason", "fieldtype": "Link", "label": "Reject Reason", "mandatory_depends_on": "eval: doc.status == \"Reject\"", "options": "CRM Reject Reason"}, {"fieldname": "reject_notes", "fieldtype": "Text", "label": "Reject Notes", "mandatory_depends_on": "eval: doc.reject_reason == \"Other\""}, {"default": "1", "description": "The rate used to convert the deal’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically.", "fieldname": "exchange_rate", "fieldtype": "Float", "label": "Exchange Rate"}, {"fieldname": "expected_deal_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Expected Deal Value", "options": "currency"}, {"fieldname": "expected_closure_date", "fieldtype": "Date", "label": "Expected Closure Date"}, {"fieldname": "closed_date", "fieldtype": "Date", "label": "Closed Date"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-13 11:54:20.608489", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Deal", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "organization", "track_changes": 1}